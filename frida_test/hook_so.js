// 基于 Frida 17 的 SO 文件 Hook 脚本
// SO File Hooking Script based on Frida 17 API

console.log("=== SO 文件 Hook 工具 (Frida 17) ===");

// 全局变量存储 hook 信息
const hooks = new Map();
const moduleCache = new Map();

// 工具函数：安全获取模块
function getModuleSafely(moduleName) {
    if (moduleCache.has(moduleName)) {
        return moduleCache.get(moduleName);
    }
    
    try {
        const module = Process.getModuleByName(moduleName);
        moduleCache.set(moduleName, module);
        console.log(`[+] 成功加载模块: ${moduleName} @ ${module.base}`);
        return module;
    } catch (e) {
        console.log(`[-] 无法加载模块 ${moduleName}: ${e.message}`);
        return null;
    }
}

// 工具函数：格式化参数显示
function formatArgs(args, argCount = 6) {
    const result = [];
    for (let i = 0; i < argCount; i++) {
        try {
            const arg = args[i];
            if (arg.isNull()) {
                result.push("NULL");
            } else {
                // 尝试读取字符串
                try {
                    const str = arg.readUtf8String();
                    if (str && str.length > 0 && str.length < 256) {
                        result.push(`"${str}"`);
                    } else {
                        result.push(`0x${arg.toString(16)}`);
                    }
                } catch {
                    result.push(`0x${arg.toString(16)}`);
                }
            }
        } catch {
            result.push("?");
        }
    }
    return result.join(", ");
}

// 工具函数：格式化返回值
function formatRetval(retval) {
    if (!retval || retval.isNull()) {
        return "NULL";
    }
    
    try {
        const value = retval.toInt32();
        return `0x${retval.toString(16)} (${value})`;
    } catch {
        return `0x${retval.toString(16)}`;
    }
}

// 1. Hook 指定模块的指定函数
function hookFunction(moduleName, functionName, options = {}) {
    console.log(`\n[*] 尝试 Hook ${moduleName}!${functionName}`);
    
    const module = getModuleSafely(moduleName);
    if (!module) return false;
    
    try {
        const funcAddr = Module.findExportByName(moduleName, functionName);
        if (!funcAddr) {
            console.log(`[-] 未找到函数: ${functionName}`);
            return false;
        }
        
        const hookId = `${moduleName}!${functionName}`;
        
        const listener = Interceptor.attach(funcAddr, {
            onEnter: function(args) {
                const tid = Process.getCurrentThreadId();
                const timestamp = new Date().toISOString();
                
                console.log(`\n[${timestamp}] [TID:${tid}] --> ${hookId}`);
                
                if (options.logArgs !== false) {
                    console.log(`    参数: ${formatArgs(args, options.argCount || 6)}`);
                }
                
                // 保存参数供 onLeave 使用
                this.args = args;
                this.startTime = Date.now();
                
                // 自定义 onEnter 回调
                if (options.onEnter) {
                    try {
                        options.onEnter.call(this, args);
                    } catch (e) {
                        console.log(`[!] onEnter 回调错误: ${e.message}`);
                    }
                }
            },
            
            onLeave: function(retval) {
                const tid = Process.getCurrentThreadId();
                const duration = Date.now() - this.startTime;
                
                if (options.logReturn !== false) {
                    console.log(`[TID:${tid}] <-- ${hookId} 返回: ${formatRetval(retval)} (耗时: ${duration}ms)`);
                }
                
                // 自定义 onLeave 回调
                if (options.onLeave) {
                    try {
                        options.onLeave.call(this, retval);
                    } catch (e) {
                        console.log(`[!] onLeave 回调错误: ${e.message}`);
                    }
                }
            }
        });
        
        hooks.set(hookId, listener);
        console.log(`[+] 成功 Hook: ${hookId} @ ${funcAddr}`);
        return true;
        
    } catch (e) {
        console.log(`[-] Hook 失败: ${e.message}`);
        return false;
    }
}

// 2. Hook 模块中匹配模式的所有函数
function hookFunctionsByPattern(moduleName, pattern, options = {}) {
    console.log(`\n[*] 在模块 ${moduleName} 中搜索匹配 "${pattern}" 的函数`);
    
    const module = getModuleSafely(moduleName);
    if (!module) return 0;
    
    let hookCount = 0;
    const regex = new RegExp(pattern, 'i');
    
    try {
        const exports = module.enumerateExports();
        
        exports.forEach(exp => {
            if (exp.type === 'function' && regex.test(exp.name)) {
                if (hookFunction(moduleName, exp.name, options)) {
                    hookCount++;
                }
            }
        });
        
        console.log(`[+] 成功 Hook ${hookCount} 个匹配的函数`);
        return hookCount;
        
    } catch (e) {
        console.log(`[-] 搜索函数失败: ${e.message}`);
        return 0;
    }
}

// 3. 替换函数实现
function replaceFunction(moduleName, functionName, replacement) {
    console.log(`\n[*] 尝试替换 ${moduleName}!${functionName}`);
    
    const module = getModuleSafely(moduleName);
    if (!module) return false;
    
    try {
        const funcAddr = Module.findExportByName(moduleName, functionName);
        if (!funcAddr) {
            console.log(`[-] 未找到函数: ${functionName}`);
            return false;
        }
        
        // 创建 NativeCallback
        const nativeCallback = new NativeCallback(replacement, 'pointer', ['pointer']);
        
        Interceptor.replace(funcAddr, nativeCallback);
        
        const hookId = `${moduleName}!${functionName}`;
        console.log(`[+] 成功替换: ${hookId} @ ${funcAddr}`);
        return true;
        
    } catch (e) {
        console.log(`[-] 替换失败: ${e.message}`);
        return false;
    }
}

// 4. 移除指定的 Hook
function unhookFunction(moduleName, functionName) {
    const hookId = `${moduleName}!${functionName}`;
    
    if (hooks.has(hookId)) {
        hooks.get(hookId).detach();
        hooks.delete(hookId);
        console.log(`[+] 已移除 Hook: ${hookId}`);
        return true;
    } else {
        console.log(`[-] 未找到 Hook: ${hookId}`);
        return false;
    }
}

// 5. 移除所有 Hook
function unhookAll() {
    console.log(`\n[*] 移除所有 Hook (共 ${hooks.size} 个)`);
    
    hooks.forEach((listener, hookId) => {
        listener.detach();
        console.log(`[+] 已移除: ${hookId}`);
    });
    
    hooks.clear();
    Interceptor.detachAll();
    console.log("[+] 所有 Hook 已移除");
}

// 6. 列出当前所有 Hook
function listHooks() {
    console.log(`\n=== 当前 Hook 列表 (共 ${hooks.size} 个) ===`);
    
    if (hooks.size === 0) {
        console.log("无活动 Hook");
        return;
    }
    
    hooks.forEach((listener, hookId) => {
        console.log(`  - ${hookId}`);
    });
}

// 导出函数供外部使用
const SoHooker = {
    hookFunction,
    hookFunctionsByPattern,
    replaceFunction,
    unhookFunction,
    unhookAll,
    listHooks,
    getModuleSafely,
    formatArgs,
    formatRetval
};

// 使用示例
console.log("\n=== 使用示例 ===");

// 根据平台选择示例
if (Process.platform === 'linux') {
    // Linux 示例
    console.log("Linux 平台示例:");
    console.log("SoHooker.hookFunction('libc.so.6', 'malloc');");
    console.log("SoHooker.hookFunctionsByPattern('libc.so.6', 'str.*');");
    
} else if (Process.platform === 'darwin') {
    // macOS 示例
    console.log("macOS 平台示例:");
    console.log("SoHooker.hookFunction('libSystem.B.dylib', 'malloc');");
    console.log("SoHooker.hookFunctionsByPattern('libSystem.B.dylib', 'str.*');");
    
} else if (Process.platform === 'windows') {
    // Windows 示例
    console.log("Windows 平台示例:");
    console.log("SoHooker.hookFunction('kernel32.dll', 'CreateFileW');");
    console.log("SoHooker.hookFunctionsByPattern('kernel32.dll', 'Create.*');");
}

console.log("\n其他命令:");
console.log("SoHooker.listHooks();           // 列出所有 Hook");
console.log("SoHooker.unhookAll();           // 移除所有 Hook");

// 7. 内存读写辅助函数
function readMemory(address, size, format = 'hex') {
    try {
        const ptr = ptr(address);
        const data = ptr.readByteArray(size);

        switch (format) {
            case 'hex':
                return hexdump(data, { ansi: true });
            case 'string':
                return ptr.readUtf8String(size);
            case 'bytes':
                return Array.from(new Uint8Array(data));
            default:
                return data;
        }
    } catch (e) {
        console.log(`[-] 读取内存失败: ${e.message}`);
        return null;
    }
}

// 8. 栈回溯功能
function printStackTrace(context) {
    try {
        const backtrace = Thread.backtrace(context, Backtracer.ACCURATE);
        console.log("    栈回溯:");

        backtrace.forEach((frame, index) => {
            const module = Process.findModuleByAddress(frame);
            const moduleName = module ? module.name : "未知模块";
            const offset = module ? frame.sub(module.base) : frame;

            console.log(`      ${index}: ${frame} ${moduleName}+0x${offset.toString(16)}`);
        });
    } catch (e) {
        console.log(`    栈回溯失败: ${e.message}`);
    }
}

// 9. 高级 Hook 选项
function hookFunctionAdvanced(moduleName, functionName, options = {}) {
    console.log(`\n[*] 高级 Hook ${moduleName}!${functionName}`);

    const module = getModuleSafely(moduleName);
    if (!module) return false;

    try {
        const funcAddr = Module.findExportByName(moduleName, functionName);
        if (!funcAddr) {
            console.log(`[-] 未找到函数: ${functionName}`);
            return false;
        }

        const hookId = `${moduleName}!${functionName}`;

        const listener = Interceptor.attach(funcAddr, {
            onEnter: function(args) {
                const tid = Process.getCurrentThreadId();
                const timestamp = new Date().toISOString();

                console.log(`\n[${timestamp}] [TID:${tid}] --> ${hookId}`);

                // 参数日志
                if (options.logArgs !== false) {
                    console.log(`    参数: ${formatArgs(args, options.argCount || 6)}`);
                }

                // 栈回溯
                if (options.backtrace) {
                    printStackTrace(this.context);
                }

                // 内存转储
                if (options.dumpMemory) {
                    options.dumpMemory.forEach(dump => {
                        const addr = args[dump.argIndex] || dump.address;
                        if (addr && !addr.isNull()) {
                            console.log(`    内存转储 [${dump.name || 'Memory'}]:`);
                            console.log(readMemory(addr, dump.size || 64, dump.format || 'hex'));
                        }
                    });
                }

                // 条件过滤
                if (options.condition) {
                    if (!options.condition.call(this, args)) {
                        return; // 不满足条件，跳过后续处理
                    }
                }

                this.args = args;
                this.startTime = Date.now();

                if (options.onEnter) {
                    options.onEnter.call(this, args);
                }
            },

            onLeave: function(retval) {
                const tid = Process.getCurrentThreadId();
                const duration = Date.now() - this.startTime;

                if (options.logReturn !== false) {
                    console.log(`[TID:${tid}] <-- ${hookId} 返回: ${formatRetval(retval)} (耗时: ${duration}ms)`);
                }

                // 修改返回值
                if (options.modifyReturn) {
                    const newRetval = options.modifyReturn.call(this, retval);
                    if (newRetval !== undefined) {
                        retval.replace(ptr(newRetval));
                        console.log(`    返回值已修改为: ${formatRetval(retval)}`);
                    }
                }

                if (options.onLeave) {
                    options.onLeave.call(this, retval);
                }
            }
        });

        hooks.set(hookId, listener);
        console.log(`[+] 成功高级 Hook: ${hookId} @ ${funcAddr}`);
        return true;

    } catch (e) {
        console.log(`[-] 高级 Hook 失败: ${e.message}`);
        return false;
    }
}

// 10. 模块监控
function watchModule(moduleName, options = {}) {
    console.log(`\n[*] 开始监控模块: ${moduleName}`);

    const observer = Process.attachModuleObserver({
        onAdded: function(module) {
            if (module.name === moduleName || module.path.includes(moduleName)) {
                console.log(`[+] 模块已加载: ${module.name} @ ${module.base}`);

                if (options.autoHook) {
                    // 自动 Hook 指定的函数
                    options.autoHook.forEach(funcName => {
                        setTimeout(() => {
                            hookFunction(module.name, funcName, options.hookOptions || {});
                        }, 100);
                    });
                }

                if (options.onLoaded) {
                    options.onLoaded(module);
                }
            }
        },

        onRemoved: function(module) {
            if (module.name === moduleName || module.path.includes(moduleName)) {
                console.log(`[-] 模块已卸载: ${module.name}`);

                if (options.onUnloaded) {
                    options.onUnloaded(module);
                }
            }
        }
    });

    return observer;
}

// 更新 SoHooker 对象
Object.assign(SoHooker, {
    readMemory,
    printStackTrace,
    hookFunctionAdvanced,
    watchModule
});

// 将 SoHooker 暴露到全局作用域
global.SoHooker = SoHooker;

console.log("\n[+] SO Hook 工具已就绪！使用 SoHooker 对象进行操作。");
console.log("\n=== 高级功能示例 ===");
console.log("// 带栈回溯的 Hook");
console.log("SoHooker.hookFunctionAdvanced('libc.so.6', 'malloc', { backtrace: true });");
console.log("\n// 带内存转储的 Hook");
console.log("SoHooker.hookFunctionAdvanced('libc.so.6', 'strcpy', {");
console.log("    dumpMemory: [");
console.log("        { argIndex: 0, size: 32, name: 'dest' },");
console.log("        { argIndex: 1, size: 32, name: 'src' }");
console.log("    ]");
console.log("});");
console.log("\n// 条件过滤 Hook");
console.log("SoHooker.hookFunctionAdvanced('libc.so.6', 'open', {");
console.log("    condition: function(args) {");
console.log("        const path = args[0].readUtf8String();");
console.log("        return path && path.includes('.so');");
console.log("    }");
console.log("});");
