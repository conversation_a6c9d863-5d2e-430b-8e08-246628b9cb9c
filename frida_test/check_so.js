// 查找 SO 文件的导出符号
// Find exported symbols from shared libraries (.so files)

console.log("=== SO 文件导出符号查找工具 ===");
/*
// 方法1: 枚举指定模块的所有导出符号
function enumerateModuleExports(moduleName) {
    console.log(`\n--- 枚举模块 ${moduleName} 的导出符号 ---`);
    try {
        const module = Process.getModuleByName(moduleName);
        console.log(`模块基址: ${module.base}`);
        console.log(`模块大小: ${module.size}`);
        console.log(`模块路径: ${module.path}`);

        const exports = module.enumerateExports();
        console.log(`总共找到 ${exports.length} 个导出符号:`);

        // 显示前20个导出符号
        exports.slice(0, 20).forEach((exp, index) => {
            console.log(`  ${index + 1}. ${exp.name} (${exp.type}) @ ${exp.address}`);
        });

        if (exports.length > 20) {
            console.log(`  ... 还有 ${exports.length - 20} 个符号`);
        }

        return exports;
    } catch (e) {
        console.log(`错误: 无法找到模块 ${moduleName} - ${e.message}`);
        return null;
    }
}

// 方法2: 搜索包含特定关键字的导出符号
function searchExportsByKeyword(moduleName, keyword) {
    console.log(`\n--- 在模块 ${moduleName} 中搜索包含 "${keyword}" 的符号 ---`);
    try {
        const module = Process.getModuleByName(moduleName);
        const exports = module.enumerateExports();

        const matchedExports = exports.filter(exp =>
            exp.name.toLowerCase().includes(keyword.toLowerCase())
        );

        console.log(`找到 ${matchedExports.length} 个匹配的符号:`);
        matchedExports.forEach((exp, index) => {
            console.log(`  ${index + 1}. ${exp.name} (${exp.type}) @ ${exp.address}`);
        });

        return matchedExports;
    } catch (e) {
        console.log(`错误: ${e.message}`);
        return null;
    }
}

// 方法3: 查找特定符号
function findSpecificExport(moduleName, symbolName) {
    console.log(`\n--- 查找特定符号 ${symbolName} 在模块 ${moduleName} 中 ---`);
    try {
        const address = Module.findExportByName(moduleName, symbolName);
        if (address) {
            console.log(`找到符号: ${symbolName} @ ${address}`);
            return address;
        } else {
            console.log(`未找到符号: ${symbolName}`);
            return null;
        }
    } catch (e) {
        console.log(`错误: ${e.message}`);
        return null;
    }
}
*/
// 方法4: 列出所有已加载的模块
function listAllModules() {
    console.log("\n--- 列出所有已加载的模块 ---");
    const modules = Process.enumerateModules();
    console.log(`总共加载了 ${modules.length} 个模块:`);

    modules.forEach((module, index) => {
        const name = module.name || "未知";
        const path = module.path || "未知路径";
        console.log(`  ${index + 1}. ${name}`);
        console.log(`     路径: ${path}`);
        console.log(`     基址: ${module.base}, 大小: ${module.size}`);
    });

    return modules;
}
/*
// 方法5: 查找全局导出符号
function findGlobalExport(symbolName) {
    console.log(`\n--- 查找全局导出符号 ${symbolName} ---`);
    try {
        const address = Module.getGlobalExportByName(symbolName);
        if (address) {
            console.log(`找到全局符号: ${symbolName} @ ${address}`);
            return address;
        } else {
            console.log(`未找到全局符号: ${symbolName}`);
            return null;
        }
    } catch (e) {
        console.log(`错误: ${e.message}`);
        return null;
    }
}
*/
// 使用示例
console.log("\n=== 开始符号查找 ===");

// 1. 列出所有模块
listAllModules();

// 2. 根据平台选择常见的系统库进行演示
if (Process.platform === 'linux') {
    // Linux 系统
    enumerateModuleExports("libc.so.6");
    searchExportsByKeyword("libc.so.6", "printf");
    findSpecificExport("libc.so.6", "malloc");
    findGlobalExport("open");
} else if (Process.platform === 'darwin') {
    // macOS 系统
    enumerateModuleExports("libSystem.B.dylib");
    searchExportsByKeyword("libSystem.B.dylib", "printf");
    findSpecificExport("libSystem.B.dylib", "malloc");
    findGlobalExport("open");
} else if (Process.platform === 'windows') {
    // Windows 系统
    enumerateModuleExports("kernel32.dll");
    searchExportsByKeyword("kernel32.dll", "CreateFile");
    findSpecificExport("kernel32.dll", "GetCurrentProcess");
    findGlobalExport("GetCurrentProcess");
}

// 3. 查找特定的 SO 文件 (需要根据实际情况修改)
// 取消注释下面的行来查找特定的 SO 文件
// enumerateModuleExports("your_library.so");
// searchExportsByKeyword("your_library.so", "your_function");

console.log("\n=== 符号查找完成 ===");