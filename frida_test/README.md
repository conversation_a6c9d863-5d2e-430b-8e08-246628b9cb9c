# Frida SO Hook 工具

基于 Frida 17 API 的共享库 (.so) Hook 工具集。

## 文件说明

- `check_so.js` - SO 文件导出符号查找工具
- `hook_so.js` - 主要的 SO Hook 工具库
- `hook_examples.js` - 使用示例和演示代码
- `README.md` - 本说明文件

## 快速开始

### 1. 查找 SO 文件的导出符号

```bash
# 附加到正在运行的进程
frida -p <进程ID> -l check_so.js

# 启动新进程并附加
frida -f <程序路径> -l check_so.js

# Android 应用
frida -U -f <包名> -l check_so.js
```

### 2. Hook SO 文件中的函数

```bash
# 加载 Hook 工具
frida -p <进程ID> -l hook_so.js

# 或者同时加载工具和示例
frida -p <进程ID> -l hook_so.js -l hook_examples.js
```

## 主要功能

### SoHooker 对象方法

#### 基础 Hook 功能

```javascript
// Hook 单个函数
SoHooker.hookFunction(moduleName, functionName, options);

// Hook 匹配模式的多个函数
SoHooker.hookFunctionsByPattern(moduleName, pattern, options);

// 高级 Hook (支持栈回溯、内存转储等)
SoHooker.hookFunctionAdvanced(moduleName, functionName, options);

// 替换函数实现
SoHooker.replaceFunction(moduleName, functionName, replacement);
```

#### Hook 管理

```javascript
// 移除指定 Hook
SoHooker.unhookFunction(moduleName, functionName);

// 移除所有 Hook
SoHooker.unhookAll();

// 列出当前所有 Hook
SoHooker.listHooks();
```

#### 辅助功能

```javascript
// 读取内存
SoHooker.readMemory(address, size, format);

// 打印栈回溯
SoHooker.printStackTrace(context);

// 监控模块加载
SoHooker.watchModule(moduleName, options);
```

## 使用示例

### 1. 基础函数 Hook

```javascript
// Hook malloc 函数
SoHooker.hookFunction('libc.so.6', 'malloc', {
    onEnter: function(args) {
        const size = args[0].toInt32();
        console.log(`分配内存: ${size} 字节`);
    },
    onLeave: function(retval) {
        console.log(`返回地址: ${retval}`);
    }
});
```

### 2. 模式匹配 Hook

```javascript
// Hook 所有以 "str" 开头的函数
SoHooker.hookFunctionsByPattern('libc.so.6', '^str.*', {
    logArgs: true,
    argCount: 3
});
```

### 3. 高级 Hook 功能

```javascript
// 带栈回溯和条件过滤的 Hook
SoHooker.hookFunctionAdvanced('libc.so.6', 'malloc', {
    backtrace: true,
    condition: function(args) {
        const size = args[0].toInt32();
        return size > 1024; // 只 Hook 大于 1KB 的分配
    },
    dumpMemory: [
        { argIndex: 0, size: 32, name: 'size_param' }
    ]
});
```

### 4. 内存转储

```javascript
// Hook strcpy 并转储内存
SoHooker.hookFunctionAdvanced('libc.so.6', 'strcpy', {
    dumpMemory: [
        { argIndex: 0, size: 32, name: 'destination' },
        { argIndex: 1, size: 32, name: 'source' }
    ]
});
```

### 5. 函数替换

```javascript
// 替换函数实现
SoHooker.replaceFunction('libc.so.6', 'strlen', function(str) {
    console.log('strlen 被调用');
    return 42; // 总是返回 42
});
```

### 6. 模块监控

```javascript
// 监控模块加载并自动 Hook
const observer = SoHooker.watchModule('libssl', {
    autoHook: ['SSL_read', 'SSL_write'],
    onLoaded: function(module) {
        console.log(`SSL 模块已加载: ${module.name}`);
    }
});
```

## Hook 选项参数

### 基础选项

- `logArgs` - 是否记录参数 (默认: true)
- `logReturn` - 是否记录返回值 (默认: true)
- `argCount` - 记录的参数数量 (默认: 6)
- `onEnter` - 进入函数时的回调
- `onLeave` - 离开函数时的回调

### 高级选项

- `backtrace` - 是否打印栈回溯
- `condition` - 条件过滤函数
- `dumpMemory` - 内存转储配置数组
- `modifyReturn` - 修改返回值的函数

### 内存转储配置

```javascript
dumpMemory: [
    {
        argIndex: 0,        // 参数索引
        address: ptr(0x123), // 或直接指定地址
        size: 64,           // 转储大小
        name: 'buffer',     // 显示名称
        format: 'hex'       // 格式: 'hex', 'string', 'bytes'
    }
]
```

## 平台支持

- **Linux**: 支持 `.so` 文件 Hook
- **macOS**: 支持 `.dylib` 文件 Hook  
- **Windows**: 支持 `.dll` 文件 Hook
- **Android**: 支持 Android 应用的 native 库 Hook

## 注意事项

1. Hook 操作可能影响程序稳定性，请谨慎使用
2. 某些系统函数可能被频繁调用，会产生大量日志
3. 使用 `condition` 选项可以过滤不需要的调用
4. 在生产环境中使用前请充分测试
5. 移除 Hook 时使用 `unhookAll()` 要特别小心

## 故障排除

### 常见问题

1. **模块未找到**: 检查模块名称是否正确，使用 `Process.enumerateModules()` 查看已加载模块
2. **函数未找到**: 使用 `check_so.js` 查看模块的导出函数
3. **Hook 失败**: 可能是权限问题或函数已被其他工具 Hook
4. **程序崩溃**: 检查 Hook 回调中的代码是否有错误

### 调试技巧

```javascript
// 列出所有模块
Process.enumerateModules().forEach(m => console.log(m.name));

// 查看模块导出
Process.getModuleByName('libc.so.6').enumerateExports().slice(0, 10);

// 检查函数地址
console.log(Module.findExportByName('libc.so.6', 'malloc'));
```

## 许可证

本工具基于 Frida 框架开发，遵循相应的开源许可证。
