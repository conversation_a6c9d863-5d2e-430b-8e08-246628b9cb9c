// SO Hook 使用示例
// 基于 Frida 17 API

// 首先加载主要的 Hook 工具
// 在实际使用时，你需要先运行 hook_so.js 或者在这里 load 它

console.log("=== SO Hook 使用示例 ===");

// 示例 1: 基础函数 Hook
function example1_basicHook() {
    console.log("\n--- 示例 1: 基础函数 Hook ---");
    
    if (Process.platform === 'linux') {
        // Hook malloc 函数
        SoHooker.hookFunction('libc.so.6', 'malloc', {
            onEnter: function(args) {
                const size = args[0].toInt32();
                console.log(`    请求分配内存大小: ${size} 字节`);
            },
            onLeave: function(retval) {
                console.log(`    分配的内存地址: ${retval}`);
            }
        });
        
        // Hook free 函数
        SoHooker.hookFunction('libc.so.6', 'free', {
            onEnter: function(args) {
                const ptr = args[0];
                console.log(`    释放内存地址: ${ptr}`);
            }
        });
        
    } else if (Process.platform === 'darwin') {
        // macOS 版本
        SoHooker.hookFunction('libSystem.B.dylib', 'malloc');
        SoHooker.hookFunction('libSystem.B.dylib', 'free');
        
    } else if (Process.platform === 'windows') {
        // Windows 版本
        SoHooker.hookFunction('kernel32.dll', 'CreateFileW', {
            onEnter: function(args) {
                try {
                    const filename = args[0].readUtf16String();
                    console.log(`    打开文件: ${filename}`);
                } catch (e) {
                    console.log(`    文件名读取失败: ${e.message}`);
                }
            }
        });
    }
}

// 示例 2: 模式匹配 Hook
function example2_patternHook() {
    console.log("\n--- 示例 2: 模式匹配 Hook ---");
    
    if (Process.platform === 'linux') {
        // Hook 所有以 "str" 开头的函数
        SoHooker.hookFunctionsByPattern('libc.so.6', '^str.*', {
            logArgs: true,
            argCount: 3,
            onEnter: function(args) {
                // 尝试读取字符串参数
                for (let i = 0; i < 3; i++) {
                    try {
                        const str = args[i].readUtf8String();
                        if (str && str.length > 0 && str.length < 100) {
                            console.log(`    字符串参数 ${i}: "${str}"`);
                        }
                    } catch (e) {
                        // 忽略非字符串参数
                    }
                }
            }
        });
    }
}

// 示例 3: 高级 Hook 功能
function example3_advancedHook() {
    console.log("\n--- 示例 3: 高级 Hook 功能 ---");
    
    if (Process.platform === 'linux') {
        // 带栈回溯的 Hook
        SoHooker.hookFunctionAdvanced('libc.so.6', 'malloc', {
            backtrace: true,
            condition: function(args) {
                const size = args[0].toInt32();
                return size > 1024; // 只 Hook 大于 1KB 的分配
            },
            onEnter: function(args) {
                const size = args[0].toInt32();
                console.log(`    大内存分配: ${size} 字节`);
            }
        });
        
        // 带内存转储的 Hook
        SoHooker.hookFunctionAdvanced('libc.so.6', 'strcpy', {
            dumpMemory: [
                { argIndex: 0, size: 32, name: 'destination' },
                { argIndex: 1, size: 32, name: 'source' }
            ],
            onEnter: function(args) {
                try {
                    const src = args[1].readUtf8String();
                    console.log(`    复制字符串: "${src}"`);
                } catch (e) {
                    console.log(`    字符串读取失败: ${e.message}`);
                }
            }
        });
    }
}

// 示例 4: 函数替换
function example4_functionReplace() {
    console.log("\n--- 示例 4: 函数替换 ---");
    
    if (Process.platform === 'linux') {
        // 替换 strlen 函数，总是返回固定值
        SoHooker.replaceFunction('libc.so.6', 'strlen', function(str) {
            console.log(`[替换] strlen 被调用`);
            
            // 调用原始实现
            const originalStrlen = Module.findExportByName('libc.so.6', 'strlen');
            const result = new NativeFunction(originalStrlen, 'size_t', ['pointer'])(str);
            
            console.log(`[替换] 原始长度: ${result}, 返回固定值: 42`);
            return 42; // 总是返回 42
        });
    }
}

// 示例 5: 模块监控
function example5_moduleWatch() {
    console.log("\n--- 示例 5: 模块监控 ---");
    
    // 监控特定模块的加载
    const observer = SoHooker.watchModule('libssl', {
        autoHook: ['SSL_read', 'SSL_write'], // 自动 Hook 这些函数
        hookOptions: {
            logArgs: true,
            onEnter: function(args) {
                console.log(`    SSL 操作被调用`);
            }
        },
        onLoaded: function(module) {
            console.log(`    SSL 模块已加载，开始监控加密通信`);
        }
    });
    
    // 5 秒后停止监控
    setTimeout(() => {
        observer.detach();
        console.log(`    停止模块监控`);
    }, 5000);
}

// 示例 6: 内存操作
function example6_memoryOps() {
    console.log("\n--- 示例 6: 内存操作 ---");
    
    // 分配一些测试内存
    const testMem = Memory.alloc(64);
    testMem.writeUtf8String("Hello, Frida!");
    
    // 读取内存
    console.log("内存内容 (hex):");
    console.log(SoHooker.readMemory(testMem, 32, 'hex'));
    
    console.log("内存内容 (string):");
    console.log(SoHooker.readMemory(testMem, 13, 'string'));
    
    console.log("内存内容 (bytes):");
    console.log(SoHooker.readMemory(testMem, 13, 'bytes'));
}

// 示例 7: Hook 管理
function example7_hookManagement() {
    console.log("\n--- 示例 7: Hook 管理 ---");
    
    // 列出当前所有 Hook
    SoHooker.listHooks();
    
    // 移除特定 Hook
    if (Process.platform === 'linux') {
        SoHooker.unhookFunction('libc.so.6', 'malloc');
    }
    
    // 再次列出 Hook
    SoHooker.listHooks();
    
    // 移除所有 Hook (谨慎使用)
    // SoHooker.unhookAll();
}

// 主函数
function runExamples() {
    console.log("开始运行 SO Hook 示例...\n");
    
    // 检查 SoHooker 是否可用
    if (typeof SoHooker === 'undefined') {
        console.log("错误: SoHooker 未定义，请先加载 hook_so.js");
        return;
    }
    
    // 运行示例 (可以选择性运行)
    example1_basicHook();
    
    setTimeout(() => example2_patternHook(), 1000);
    setTimeout(() => example3_advancedHook(), 2000);
    setTimeout(() => example6_memoryOps(), 3000);
    setTimeout(() => example7_hookManagement(), 4000);
    
    // 高级示例 (可能需要特定环境)
    // example4_functionReplace();
    // example5_moduleWatch();
    
    console.log("\n所有示例已启动，观察输出...");
}

// 如果直接运行此脚本
if (typeof SoHooker !== 'undefined') {
    runExamples();
} else {
    console.log("请先加载 hook_so.js，然后运行:");
    console.log("runExamples();");
}

// 导出函数供外部调用
global.runExamples = runExamples;
global.example1_basicHook = example1_basicHook;
global.example2_patternHook = example2_patternHook;
global.example3_advancedHook = example3_advancedHook;
global.example4_functionReplace = example4_functionReplace;
global.example5_moduleWatch = example5_moduleWatch;
global.example6_memoryOps = example6_memoryOps;
global.example7_hookManagement = example7_hookManagement;
